# Cloudflare Pages Git 部署指南

本指南将引导您完成通过连接到 GitHub 仓库来自动部署您的项目的完整流程。这是最推荐的部署方式。

### **前提条件**
*   您拥有一个 [GitHub](https://github.com/) 账户。
*   您的项目代码已经在本机准备就绪。

---

### **第一步：在本地初始化 Git 并提交代码**

首先，我们需要在您的项目目录中初始化一个 Git 仓库，并将所有文件添加到版本控制中。

打开终端，进入项目根目录 (`d:/pycode/vercel_learn`)，然后执行以下命令：

```bash
# 1. 初始化 Git 仓库
git init

# 2. 将所有文件添加到暂存区
git add .

# 3. 创建第一个提交记录
git commit -m "Initial commit of the project"
```

---

### **第二步：在 GitHub 上创建远程仓库**

1.  登录您的 GitHub 账户。
2.  点击右上角的 `+` 号，选择 `New repository`。
3.  **Repository name**: 给您的仓库起一个名字（例如 `cloudflare-mysql-app`）。
4.  **Description**: (可选) 添加项目描述。
5.  选择 `Public` 或 `Private`。
6.  **非常重要**：**不要**勾选 "Add a README file", "Add .gitignore", 或 "Choose a license"，因为我们的项目已经包含这些文件了。
7.  点击 `Create repository`。

---

### **第三步：关联本地与远程仓库并推送**

创建仓库后，GitHub 会显示一个标题为 "...or push an existing repository from the command line" 的部分。复制并执行这些命令，将您的本地代码推送到 GitHub。

```bash
# 1. 关联远程仓库 (请将 URL 替换为您自己的仓库 URL)
git remote add origin https://github.com/YOUR_USERNAME/YOUR_REPOSITORY.git

# 2. 确保您的主分支名为 'main'
git branch -M main

# 3. 将代码推送到 GitHub
git push -u origin main
```

---

### **第四步：在 Cloudflare 上创建 Pages 项目**

1.  登录您的 [Cloudflare 仪表盘](https://dash.cloudflare.com/)。
2.  在左侧导航栏中，选择 `Workers & Pages`。
3.  点击 `Create application` -> 选择 `Pages` 选项卡 -> 点击 `Connect to Git`。
4.  选择您的 GitHub 账户进行连接，并授权 Cloudflare 访问您刚创建的仓库。
5.  找到并选择您的仓库，然后点击 `Begin setup`。

---

### **第五步：配置构建和部署**

在配置页面，您需要告诉 Cloudflare 如何“构建”您的项目。

1.  **Project name**: Cloudflare 会自动填充，您可以修改它。
2.  **Production branch**: 确保选择的是 `main`。
3.  **Build settings**:
    *   **Framework preset**: 选择 `None`。
    *   **Build command**: **将此项留空**。我们的项目不需要编译步骤。
    *   **Build output directory**: 设置为 `public`。这是存放您 `index.html` 的目录。
4.  点击 `Save and Deploy`。Cloudflare 将开始您的第一次部署。

---

### **第六步：设置数据库环境变量**

首次部署可能会因为缺少数据库连接信息而失败或无法正常工作，这是正常的。现在我们需要安全地添加它。

1.  在您的 Pages 项目页面，点击 `Settings` -> `Environment variables`。
2.  在 **Production** 环境下，点击 `Add variable`。
3.  **Variable name**: `DATABASE_URL`
4.  **Value**: 输入您的数据库连接字符串。**点击 `Encrypt` 按钮将其加密为 Secret**，这是保护密码的最佳方式。
    *   格式: `mysql://USER:PASSWORD@HOST:PORT/DATABASE`
    *   *请务必将上述占位符替换为您真实的数据库信息。*
5.  保存后，返回您的部署页面，找到最新的那次部署，并点击 `Retry deployment` 以使用新的环境变量重新部署。

---

### **第七步：大功告成！以及如何更新**

您的网站现在已经成功部署了！

从现在开始，您的开发流程将变得极其简单。每当您在本地修改了代码，只需执行以下三个命令：

```bash
# 1. 添加修改
git add .

# 2. 提交修改
git commit -m "描述您的更新内容"

# 3. 推送到 GitHub
git push origin main
```

Cloudflare 会自动检测到新的推送，并为您完成后续所有的部署工作。