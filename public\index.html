<!DOCTYPE html>
<html>
<head>
    <title>电力项目信息网(测试中)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            background-color: #f0f0f0;
        }
        table {
            width: 80%;
            margin: 0 auto;
            border-collapse: collapse;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #fff;
        }
        th {
            background-color: #333;
            color: #fff;
        }
        a {
            text-decoration: none;
            color: #007BFF;
        }
        .pagination {
            margin: 20px auto;
            display: flex;
            justify-content: center;
        }
        .pagination a {
            margin: 0 5px;
            text-decoration: none;
            color: #007BFF;
        }
        .pagination a.active {
            font-weight: bold;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <h1>电力项目招标信息网</h1><h2>测试中......</h2>
    <table>
        <thead>
            <tr>
                <th>序号</th> 
                <th>发布日期</th>
                <th>项目名称</th>
                <th>截标日期</th>
                <th>资金来源</th>
                <th>国家</th>
                <th>贷款编号</th>
            </tr>
        </thead>
        <tbody id="tender-data">
            <!-- Data will be loaded here -->
        </tbody>
    </table>
    <div class="pagination" id="pagination-controls">
        <!-- Pagination controls will be loaded here -->
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const urlParams = new URLSearchParams(window.location.search);
            const page = parseInt(urlParams.get('page') || '1', 10);
            fetchData(page);
        });

        async function fetchData(page) {
            try {
                const response = await fetch(`/api?page=${page}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const result = await response.json();
                renderTable(result.data, result.offset);
                renderPagination(result.page, result.total_pages);
            } catch (error) {
                console.error('Error fetching data:', error);
                document.getElementById('tender-data').innerHTML = `<tr><td colspan="7">Error fetching data: ${error.message}</td></tr>`;
            }
        }

        function renderTable(data, offset) {
            const tbody = document.getElementById('tender-data');
            tbody.innerHTML = '';
            data.forEach((row, index) => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${offset + index + 1}</td> 
                    <td>${row.postdate || ''}</td>
                    <td><a href="${row.link || '#'}" target="_blank">${row.title || ''}</a></td>
                    <td>${row.closingdate || '见招标文件'}</td>
                    <td>${row.sourceoffunds || '见招标文件'}</td>
                    <td>${row.country || '见招标文件'}</td>
                    <td>${row.loannumber || '见招标文件'}</td>
                `;
                tbody.appendChild(tr);
            });
        }

        function renderPagination(currentPage, totalPages) {
            const pagination = document.getElementById('pagination-controls');
            pagination.innerHTML = '';

            if (currentPage > 1) {
                const prevLink = document.createElement('a');
                prevLink.href = `?page=${currentPage - 1}`;
                prevLink.textContent = 'Previous';
                pagination.appendChild(prevLink);
            }

            for (let p = 1; p <= totalPages; p++) {
                const pageLink = document.createElement('a');
                pageLink.href = `?page=${p}`;
                pageLink.textContent = p;
                if (p === currentPage) {
                    pageLink.classList.add('active');
                }
                pagination.appendChild(pageLink);
            }

            if (currentPage < totalPages) {
                const nextLink = document.createElement('a');
                nextLink.href = `?page=${currentPage + 1}`;
                nextLink.textContent = 'Next';
                pagination.appendChild(nextLink);
            }
        }
    </script>
</body>
</html>
