# 项目重构与 Cloudflare 部署计划

## 1. 现状分析

*   **前端**: 一个位于 `public/index.html` 的静态页面，使用 JavaScript `fetch` API 从后端获取数据。
*   **后端**: 一个位于 `api/index.js` 的 Node.js 无服务器函数，依赖 `mysql2` 包连接 MySQL 数据库。
*   **主要问题**: 数据库凭证硬编码在后端代码中，存在严重的安全风险。
*   **平台**: 当前结构为 Vercel 设计，需要调整以适应 Cloudflare。

## 2. 重构与部署计划

### 第一阶段：项目结构重构

采用更适合 Cloudflare 的目录结构，以实现前后端逻辑的清晰分离和平台特性的最佳利用。

**建议的新项目结构：**
```
.
├── .gitignore
├── package.json
├── wrangler.toml         # Cloudflare Workers 配置文件
├── functions/
│   └── api/
│       └── [[path]].js   # Cloudflare Pages Functions (替代 Vercel API)
└── public/
    ├── index.html
    └── ... (其他静态资源)
```

### 第二阶段：代码迁移与优化

1.  **后端迁移**:
    *   将 `api/index.js` 的逻辑迁移到新的 `functions/api/[[path]].js`。
    *   适配 Cloudflare Workers 的运行时环境。
    *   **安全加固**: 移除硬编码的数据库凭证，并配置为从 Cloudflare 的环境变量或 Secrets 中读取。
2.  **前端调整**:
    *   前端代码无需大的改动，API 请求路径 `/api` 将被 Cloudflare Pages Functions 自动处理。

### 第三阶段：部署到 Cloudflare

1.  **配置 `wrangler.toml`**: 定义项目、构建设置，并安全地绑定数据库凭证。
2.  **部署**: 通过 `wrangler` CLI 或 Git 集成的方式进行部署。

## 3. 重构后的架构图

```mermaid
graph TD
    subgraph "用户浏览器"
        A["index.html"] -- "fetch('/api?page=1')" --> B{Cloudflare Pages};
    end

    subgraph "Cloudflare 平台"
        B -- "路由到 Functions" --> C["functions/api/[[path]].js"];
        C -- "查询数据" --> D[(MySQL 数据库)];
        C -- "绑定环境变量" --> E[Cloudflare Secrets];
        E -- "包含" --> F[数据库凭证];
    end

    C -- "返回 JSON 数据" --> B;
    B -- "返回 HTML/JS/CSS" --> A;