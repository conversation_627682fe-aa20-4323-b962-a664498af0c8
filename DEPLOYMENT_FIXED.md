# Cloudflare MySQL App 部署修复指南

## 问题分析

您的项目部署失败的主要原因是 **Node.js 内置模块兼容性问题**。`mysql2` 库依赖大量 Node.js 内置模块（如 `util`, `crypto`, `net`, `tls`, `events` 等），这些模块在 Cloudflare Workers 运行时环境中不可用。

## 解决方案

已将项目更新为使用 `@planetscale/database` 客户端，它专为 edge 运行时环境设计。

## 修复后的项目结构

```
cloudflare-mysql-app/
├── functions/
│   ├── _worker.js          # Cloudflare Workers 配置
│   └── api/
│       └── [[path]].js     # API 路由处理器（已修复）
├── public/
│   └── index.html          # 静态前端页面
├── package.json            # 更新依赖为 @planetscale/database
├── wrangler.toml          # 新增配置文件
└── DEPLOYMENT_FIXED.md    # 本指南
```

## 详细部署步骤

### 第一步：准备代码

1. **确认依赖已安装**：
```bash
npm install
```

2. **提交修复后的代码**：
```bash
git add .
git commit -m "Fix: Replace mysql2 with @planetscale/database for Cloudflare compatibility"
git push origin main
```

### 第二步：登录 Cloudflare Dashboard

1. 打开浏览器，访问：https://dash.cloudflare.com/
2. 使用您的 Cloudflare 账户登录
3. 如果没有账户，需要先注册一个免费账户

### 第三步：创建 Pages 项目

1. **进入 Pages 页面**：
   - 在左侧导航栏中，点击 `Workers & Pages`
   - 点击右上角的 `Create application` 按钮
   - 选择 `Pages` 选项卡
   - 点击 `Connect to Git`

2. **连接 GitHub 仓库**：
   - 选择 `GitHub` 作为 Git 提供商
   - 如果首次使用，需要授权 Cloudflare 访问您的 GitHub
   - 在仓库列表中找到您的项目仓库
   - 点击仓库名称旁边的 `Begin setup`

### 第四步：配置构建设置

在项目配置页面，按以下设置：

1. **项目名称**：
   - `Project name`: 可以保持默认或修改为 `cloudflare-mysql-app`

2. **生产分支**：
   - `Production branch`: 选择 `main`

3. **构建设置**：
   - `Framework preset`: 选择 `None`
   - `Build command`: **留空**（不要填写任何内容）
   - `Build output directory`: 填写 `public`

4. 点击 `Save and Deploy`

**⚠️ 注意**：首次部署会失败，这是正常的，因为还没有设置环境变量。

### 第五步：设置环境变量（重要！）

1. **进入项目设置**：
   - 部署完成后，在项目页面点击 `Settings` 选项卡
   - 在左侧菜单中选择 `Environment variables`

2. **添加数据库环境变量**：

   **对于 Production 环境**，依次添加以下 4 个变量：

   **变量 1**：
   - `Variable name`: `DATABASE_HOST`
   - `Value`: 您的数据库主机地址（如：`mysql.example.com` 或 IP 地址）
   - **重要**：点击 `Encrypt` 按钮（推荐加密敏感信息）
   - 点击 `Save`

   **变量 2**：
   - `Variable name`: `DATABASE_USERNAME`
   - `Value`: 您的数据库用户名
   - 点击 `Encrypt` 按钮
   - 点击 `Save`

   **变量 3**：
   - `Variable name`: `DATABASE_PASSWORD`
   - `Value`: 您的数据库密码
   - **必须**点击 `Encrypt` 按钮（密码必须加密）
   - 点击 `Save`

   **变量 4**：
   - `Variable name`: `DATABASE_NAME`
   - `Value`: 您的数据库名称
   - 点击 `Save`

### 第六步：重新部署

1. **触发重新部署**：
   - 回到项目主页面，点击 `Deployments` 选项卡
   - 找到最新的部署记录
   - 点击右侧的三个点 `...`
   - 选择 `Retry deployment`

2. **等待部署完成**：
   - 部署过程通常需要 1-3 分钟
   - 状态会从 `Building` → `Deploying` → `Success`

### 第七步：测试部署

1. **获取部署 URL**：
   - 部署成功后，您会看到类似这样的 URL：
   - `https://your-project-name.pages.dev`

2. **测试网站**：
   - 点击 URL 访问您的网站
   - 应该能看到 "电力项目招标信息网" 页面

3. **测试 API**：
   - 直接访问：`https://your-project-name.pages.dev/api/data?page=1`
   - 应该返回 JSON 格式的数据

## 主要修复内容

1. **替换数据库客户端**：
   - 从 `mysql2` 改为 `@planetscale/database`
   - 更新连接方式和查询语法

2. **添加配置文件**：
   - 创建 `wrangler.toml` 配置文件
   - 设置兼容性标志

3. **更新环境变量格式**：
   - 分离数据库连接参数
   - 提供更好的错误处理

## 兼容性说明

### 为什么 mysql2 不能用？

- 依赖 Node.js 内置模块（net, tls, crypto 等）
- Cloudflare Workers 运行时不完全支持这些模块
- 导致 30+ 构建错误

### @planetscale/database 的优势

- 专为 edge 运行时设计
- 使用 HTTP 连接而非 TCP
- 与 Cloudflare Workers 完全兼容
- 提供相似的 API 接口

## 故障排除

如果仍然遇到问题：

1. 检查环境变量是否正确设置
2. 确保数据库允许外部连接
3. 验证 wrangler.toml 配置
4. 查看 Cloudflare 部署日志

## 重要注意事项

### 🔐 环境变量安全
- **密码必须加密**：`DATABASE_PASSWORD` 必须点击 `Encrypt` 按钮
- **建议加密所有敏感信息**：包括 `DATABASE_HOST` 和 `DATABASE_USERNAME`
- 加密后的变量在界面上会显示为 `[ENCRYPTED]`

### 📝 数据库连接信息格式
确保您的数据库信息格式正确：
```
DATABASE_HOST=mysql.example.com        # 不要包含端口号
DATABASE_USERNAME=your_username         # 数据库用户名
DATABASE_PASSWORD=your_password         # 数据库密码
DATABASE_NAME=your_database_name        # 数据库名称
```

### 🌐 数据库访问权限
- 确保您的数据库允许外部连接
- 如果使用云数据库，需要在防火墙中允许 Cloudflare 的 IP 范围
- 或者设置为允许所有 IP（0.0.0.0/0）用于测试

### 🚨 常见问题排查

**问题 1：部署成功但 API 返回 500 错误**
- 检查环境变量是否正确设置
- 检查数据库连接信息是否正确
- 查看 Cloudflare 部署日志

**问题 2：数据库连接失败**
- 确认数据库服务器正在运行
- 检查数据库用户权限
- 验证数据库名称是否存在

**问题 3：前端显示但没有数据**
- 检查 API 路径是否正确（应该是 `/api/data`）
- 检查数据库表 `tender_info` 是否存在
- 检查表中是否有数据

### 📊 部署后验证清单

✅ 网站首页能正常访问
✅ API 接口返回正确的 JSON 数据
✅ 分页功能正常工作
✅ 数据库连接稳定
✅ 错误处理正常显示

### 🔄 后续更新流程

每次修改代码后，只需要：
```bash
git add .
git commit -m "描述您的更改"
git push origin main
```

Cloudflare 会自动检测到更改并重新部署。

现在您的项目应该可以成功部署到 Cloudflare Pages 了！
