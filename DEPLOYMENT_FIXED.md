# Cloudflare MySQL App 部署修复指南

## 问题分析

您的项目部署失败的主要原因是 **Node.js 内置模块兼容性问题**。`mysql2` 库依赖大量 Node.js 内置模块（如 `util`, `crypto`, `net`, `tls`, `events` 等），这些模块在 Cloudflare Workers 运行时环境中不可用。

## 解决方案

已将项目更新为使用 `@planetscale/database` 客户端，它专为 edge 运行时环境设计。

## 修复后的项目结构

```
cloudflare-mysql-app/
├── functions/
│   ├── _worker.js          # Cloudflare Workers 配置
│   └── api/
│       └── [[path]].js     # API 路由处理器（已修复）
├── public/
│   └── index.html          # 静态前端页面
├── package.json            # 更新依赖为 @planetscale/database
├── wrangler.toml          # 新增配置文件
└── DEPLOYMENT_FIXED.md    # 本指南
```

## 部署步骤

### 1. 安装新的依赖

```bash
npm install
```

### 2. 配置环境变量

在 Cloudflare Pages 项目设置中添加以下环境变量：

```
DATABASE_HOST=your-database-host
DATABASE_USERNAME=your-username
DATABASE_PASSWORD=your-password
DATABASE_NAME=your-database-name
```

**重要变化：** 不再使用单一的 `DATABASE_URL`，而是分别设置各个连接参数。

### 3. 部署

#### 方法一：通过 Git 连接（推荐）

1. 提交修复后的代码：
```bash
git add .
git commit -m "Fix: Replace mysql2 with @planetscale/database for Cloudflare compatibility"
git push origin main
```

2. 在 Cloudflare Dashboard 中：
   - 更新环境变量（使用新的格式）
   - 重新部署

#### 方法二：使用 Wrangler CLI

```bash
npm run deploy
```

### 4. 测试

部署成功后，测试 API：
```
https://your-app.pages.dev/api/data?page=1
```

## 主要修复内容

1. **替换数据库客户端**：
   - 从 `mysql2` 改为 `@planetscale/database`
   - 更新连接方式和查询语法

2. **添加配置文件**：
   - 创建 `wrangler.toml` 配置文件
   - 设置兼容性标志

3. **更新环境变量格式**：
   - 分离数据库连接参数
   - 提供更好的错误处理

## 兼容性说明

### 为什么 mysql2 不能用？

- 依赖 Node.js 内置模块（net, tls, crypto 等）
- Cloudflare Workers 运行时不完全支持这些模块
- 导致 30+ 构建错误

### @planetscale/database 的优势

- 专为 edge 运行时设计
- 使用 HTTP 连接而非 TCP
- 与 Cloudflare Workers 完全兼容
- 提供相似的 API 接口

## 故障排除

如果仍然遇到问题：

1. 检查环境变量是否正确设置
2. 确保数据库允许外部连接
3. 验证 wrangler.toml 配置
4. 查看 Cloudflare 部署日志

现在您的项目应该可以成功部署到 Cloudflare Pages 了！
