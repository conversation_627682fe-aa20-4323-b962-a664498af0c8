import { connect } from '@planetscale/database';

export async function onRequest(context) {
    // context 对象包含了请求、环境变数等信息
    const { request, env } = context;

    // 从环境变量中读取数据库连接配置
    const config = {
        host: env.DATABASE_HOST,
        username: env.DATABASE_USERNAME,
        password: env.DATABASE_PASSWORD,
        database: env.DATABASE_NAME,
    };

    // 检查必要的环境变量
    if (!config.host || !config.username || !config.password || !config.database) {
        const errorResponse = {
            error: 'Missing required database environment variables: DATABASE_HOST, DATABASE_USERNAME, DATABASE_PASSWORD, DATABASE_NAME'
        };
        return new Response(JSON.stringify(errorResponse), {
            headers: { 'Content-Type': 'application/json' },
            status: 500
        });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const per_page = 20;
    const offset = (page - 1) * per_page;

    try {
        // 创建数据库连接
        const connection = connect(config);

        // 查询分页数据
        const query = `SELECT * FROM tender_info ORDER BY postdate DESC LIMIT ? OFFSET ?`;
        const results = await connection.execute(query, [per_page, offset]);

        // 查询总记录数
        const totalQuery = "SELECT COUNT(*) as count FROM tender_info";
        const totalResult = await connection.execute(totalQuery);
        const total_records = totalResult.rows[0].count;
        const total_pages = Math.ceil(total_records / per_page);

        // 返回 JSON 响应
        const data = {
            data: results.rows,
            page: page,
            total_pages: total_pages,
            offset: offset
        };

        return new Response(JSON.stringify(data), {
            headers: { 'Content-Type': 'application/json' },
            status: 200
        });

    } catch (error) {
        console.error('Database query error:', error);
        const errorResponse = { error: 'Error fetching data from database: ' + error.message };
        return new Response(JSON.stringify(errorResponse), {
            headers: { 'Content-Type': 'application/json' },
            status: 500
        });
    }
}