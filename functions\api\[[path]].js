import mysql from 'mysql2/promise';

export async function onRequest(context) {
    // context 对象包含了请求、环境变数等信息
    const { request, env } = context;

    // 从环境变量中读取数据库连接 URL
    const connectionUrl = env.DATABASE_URL;
    if (!connectionUrl) {
        // 如果没有提供连接 URL，返回错误
        const errorResponse = { error: 'DATABASE_URL environment variable is not set.' };
        return new Response(JSON.stringify(errorResponse), {
            headers: { 'Content-Type': 'application/json' },
            status: 500
        });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const per_page = 20;
    const offset = (page - 1) * per_page;

    let connection;
    try {
        // 创建数据库连接
        connection = await mysql.createConnection(connectionUrl);

        // 查询分页数据
        const query = `SELECT * FROM tender_info ORDER BY postdate DESC LIMIT ${per_page} OFFSET ${offset}`;
        const [results] = await connection.execute(query);

        // 查询总记录数
        const [totalRows] = await connection.execute("SELECT COUNT(*) as count FROM tender_info");
        const total_records = totalRows[0].count;
        const total_pages = Math.ceil(total_records / per_page);

        // 返回 JSON 响应
        const data = {
            data: results,
            page: page,
            total_pages: total_pages,
            offset: offset
        };
        
        return new Response(JSON.stringify(data), {
            headers: { 'Content-Type': 'application/json' },
            status: 200
        });

    } catch (error) {
        console.error('Database query error:', error);
        const errorResponse = { error: 'Error fetching data from database.' };
        return new Response(JSON.stringify(errorResponse), {
            headers: { 'Content-Type': 'application/json' },
            status: 500
        });
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}