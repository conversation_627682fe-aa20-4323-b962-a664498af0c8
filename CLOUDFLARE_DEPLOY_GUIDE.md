# 🚀 Cloudflare Pages 部署完整指南

## 📋 部署前准备清单

- ✅ 代码已修复并推送到 GitHub
- ✅ 拥有 Cloudflare 账户
- ✅ 准备好数据库连接信息

## 🔗 重要链接

- **Cloudflare Dashboard**: https://dash.cloudflare.com/
- **您的项目 GitHub 仓库**: https://github.com/YOUR_USERNAME/YOUR_REPO

---

## 第一步：登录 Cloudflare

1. 打开 https://dash.cloudflare.com/
2. 登录您的账户（如果没有账户，先注册免费账户）

## 第二步：创建 Pages 项目

### 2.1 进入 Pages 创建页面
1. 在左侧导航栏点击 `Workers & Pages`
2. 点击右上角蓝色按钮 `Create application`
3. 选择 `Pages` 选项卡
4. 点击 `Connect to Git`

### 2.2 连接 GitHub 仓库
1. 选择 `GitHub`
2. 如果首次使用，会弹出授权窗口，点击 `Authorize Cloudflare`
3. 在仓库列表中找到您的项目
4. 点击仓库右侧的 `Begin setup`

## 第三步：配置项目设置

### 3.1 基本设置
- **Project name**: `cloudflare-mysql-app` （可自定义）
- **Production branch**: `main`

### 3.2 构建设置（重要！）
```
Framework preset: None
Build command: [留空，不要填写任何内容]
Build output directory: public
```

### 3.3 点击 `Save and Deploy`

⚠️ **注意**: 首次部署会失败，这是正常的，因为还没设置环境变量。

## 第四步：设置环境变量（关键步骤）

### 4.1 进入环境变量设置
1. 部署完成后，点击 `Settings` 选项卡
2. 在左侧菜单选择 `Environment variables`

### 4.2 添加数据库环境变量

**在 Production 环境下**，依次添加以下变量：

#### 变量 1: DATABASE_HOST
```
Variable name: DATABASE_HOST
Value: 您的数据库主机地址
Environment: Production
```
- 点击 `Encrypt` 按钮（推荐）
- 点击 `Save`

#### 变量 2: DATABASE_USERNAME
```
Variable name: DATABASE_USERNAME
Value: 您的数据库用户名
Environment: Production
```
- 点击 `Encrypt` 按钮（推荐）
- 点击 `Save`

#### 变量 3: DATABASE_PASSWORD
```
Variable name: DATABASE_PASSWORD
Value: 您的数据库密码
Environment: Production
```
- **必须**点击 `Encrypt` 按钮
- 点击 `Save`

#### 变量 4: DATABASE_NAME
```
Variable name: DATABASE_NAME
Value: 您的数据库名称
Environment: Production
```
- 点击 `Save`

### 4.3 环境变量示例
```
DATABASE_HOST=mysql.example.com
DATABASE_USERNAME=myuser
DATABASE_PASSWORD=mypassword123
DATABASE_NAME=mydatabase
```

## 第五步：重新部署

1. 回到项目主页，点击 `Deployments` 选项卡
2. 找到最新的部署记录
3. 点击右侧的三个点 `⋯`
4. 选择 `Retry deployment`
5. 等待部署完成（1-3分钟）

## 第六步：测试部署

### 6.1 访问网站
- 部署成功后，点击提供的 URL
- 格式：`https://your-project-name.pages.dev`
- 应该看到 "电力项目招标信息网" 页面

### 6.2 测试 API
- 直接访问：`https://your-project-name.pages.dev/api/data?page=1`
- 应该返回 JSON 格式的数据

---

## 🔧 故障排除

### 问题 1：部署失败
**可能原因**：
- 构建设置不正确
- 代码有语法错误

**解决方案**：
- 检查构建设置是否按指南配置
- 查看部署日志中的错误信息

### 问题 2：API 返回 500 错误
**可能原因**：
- 环境变量未设置或设置错误
- 数据库连接失败

**解决方案**：
- 检查所有 4 个环境变量是否正确设置
- 验证数据库连接信息
- 确保数据库允许外部连接

### 问题 3：网站显示但无数据
**可能原因**：
- 数据库表不存在
- 数据库中没有数据
- API 路径错误

**解决方案**：
- 检查数据库表 `tender_info` 是否存在
- 确认表中有测试数据
- 验证 API 调用路径是否为 `/api/data`

---

## 📝 重要提醒

1. **密码安全**: 数据库密码必须加密存储
2. **数据库权限**: 确保数据库允许外部连接
3. **环境变量**: 所有 4 个变量都必须设置
4. **API 路径**: 前端调用的是 `/api/data` 而不是 `/api`

## 🎉 部署成功标志

- ✅ 网站首页正常显示
- ✅ 数据表格有内容显示
- ✅ 分页功能正常工作
- ✅ API 接口返回正确数据

完成以上步骤后，您的项目就成功部署到 Cloudflare Pages 了！
