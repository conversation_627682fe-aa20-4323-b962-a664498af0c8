{"name": "@planetscale/database", "version": "1.19.0", "description": "A Fetch API-compatible PlanetScale database driver", "files": ["dist"], "engines": {"node": ">=16"}, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"import": "./dist/index.js", "require": "./dist/cjs/index.js"}, "prettier": {"semi": false, "trailingComma": "none", "singleQuote": true, "printWidth": 120, "tabWidth": 2, "useTabs": false, "bracketSpacing": true}, "scripts": {"clean": "rm -rf dist/", "prebuild": "npm run clean", "build": "tsc && tsc --module commonjs --outDir dist/cjs", "postbuild": "echo '{\"type\": \"commonjs\"}' > dist/cjs/package.json", "lint": "eslint src/ __tests__/", "pretest": "npm run build", "test": "jest", "version": "echo \"export const Version = '$npm_package_version'\" > src/version.ts && git add src/version.ts && npm run build", "prepublishOnly": "npm run build", "postpublish": "npm publish --ignore-scripts --@planetscale:registry='https://npm.pkg.github.com'"}, "repository": {"type": "git", "url": "git+https://github.com/planetscale/database-js.git"}, "keywords": ["planetscale", "database", "mysql", "vitess", "serverless", "vercel", "lambda"], "author": "PlanetScale", "license": "Apache-2.0", "bugs": {"url": "https://github.com/planetscale/database-js/issues"}, "homepage": "https://github.com/planetscale/database-js#readme", "devDependencies": {"@types/jest": "^29.5.3", "@types/sqlstring": "^2.3.0", "@typescript-eslint/eslint-plugin": "^6.1.0", "@typescript-eslint/parser": "^6.1.0", "eslint": "^8.45.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.6.1", "prettier": "^3.0.0", "sqlstring": "^2.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6", "undici": "^5.8.0"}, "eslintConfig": {"extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"no-control-regex": "off", "prettier/prettier": ["error", {}, {"usePrettierrc": true}], "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/ban-ts-comment": "off"}, "root": true, "env": {"browser": true, "node": true}}}